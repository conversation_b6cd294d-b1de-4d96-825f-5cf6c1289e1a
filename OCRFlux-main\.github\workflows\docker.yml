on:
  push:
    branches:
      - main
      - 'v*.*.*'

jobs:
  build_and_push_docker:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Determine image tags
        id: determine_tags
        run: |
          BRANCH_NAME=${{ github.ref_name }}
          DOCKER_IMAGE_NAME="chatdoc/ocrflux"

          if [[ "$BRANCH_NAME" == "main" ]]; then
            echo "IMAGE_TAGS=$DOCKER_IMAGE_NAME:latest,$DOCKER_IMAGE_NAME:$BRANCH_NAME"
            echo "image_tags=$DOCKER_IMAGE_NAME:latest,$DOCKER_IMAGE_NAME:$BRANCH_NAME" >> $GITHUB_OUTPUT
          else
            echo "IMAGE_TAGS=$DOCKER_IMAGE_NAME:$BRANCH_NAME"
            echo "image_tags=$DOCKER_IMAGE_NAME:$BRANCH_NAME" >> $GITHUB_OUTPUT
          fi

      - name: Build and push Docker image
        id: docker_build
        uses: docker/build-push-action@v6
        with:
          context: .
          push: true
          tags: ${{ steps.determine_tags.outputs.image_tags }}
          cache-from: type=gha,scope=${{ github.workflow }}
          cache-to: type=gha,scope=${{ github.workflow }},mode=max
