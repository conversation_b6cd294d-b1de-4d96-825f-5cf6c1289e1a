# OCRFlux 远程VLLM服务使用指南

本文档说明如何使用修改后的OCRFlux来访问远程VLLM服务，而不是在本地启动模型。

## 主要修改

### 1. 修改的文件
- `ocrflux/inference.py` - 主要推理文件，已修改为支持远程VLLM服务

### 2. 新增功能
- 支持远程VLLM服务访问
- 异步HTTP请求处理
- 新的`RemoteVLLMClient`类
- 保持与原API的兼容性

## 使用方法

### 方式1：使用RemoteVLLMClient类（推荐）

```python
from ocrflux.inference import RemoteVLLMClient

# 创建客户端
client = RemoteVLLMClient(
    url="http://localhost:30024",  # VLLM服务地址
    model="ChatDOC/OCRFlux-3B"     # 模型名称
)

# 解析文档
result = client.parse(
    file_path="document.pdf",
    skip_cross_page_merge=False,   # 是否跳过跨页合并
    max_page_retries=3            # 最大重试次数
)

if result is not None:
    print(f"解析成功，页数: {result['num_pages']}")
    print(f"文档内容: {result['document_text']}")
else:
    print("解析失败")
```

### 方式2：直接使用parse函数

```python
from ocrflux.inference import parse

result = parse(
    url="http://localhost:30024",
    model="ChatDOC/OCRFlux-3B",
    file_path="document.pdf",
    skip_cross_page_merge=False,
    max_page_retries=3
)
```

### 方式3：使用异步版本

```python
import asyncio
from ocrflux.inference import parse_async

async def main():
    result = await parse_async(
        url="http://localhost:30024",
        model="ChatDOC/OCRFlux-3B",
        file_path="document.pdf",
        skip_cross_page_merge=False,
        max_page_retries=3
    )
    return result

# 运行异步函数
result = asyncio.run(main())
```

## 远程VLLM服务设置

### 1. 启动VLLM服务

在远程服务器上启动VLLM服务：

```bash
# 方式1：使用vllm命令
vllm serve ChatDOC/OCRFlux-3B \
    --port 30024 \
    --max-model-len 8192 \
    --gpu_memory_utilization 0.8

# 方式2：使用Python脚本
python -m vllm.entrypoints.openai.api_server \
    --model ChatDOC/OCRFlux-3B \
    --port 30024 \
    --max-model-len 8192 \
    --gpu-memory-utilization 0.8
```

### 2. 验证服务状态

```bash
# 检查服务是否正常运行
curl http://localhost:30024/v1/models

# 应该返回类似以下的JSON响应：
# {
#   "object": "list",
#   "data": [
#     {
#       "id": "ChatDOC/OCRFlux-3B",
#       "object": "model",
#       ...
#     }
#   ]
# }
```

## 配置参数说明

### RemoteVLLMClient参数

- `url`: VLLM服务的完整URL，例如 `"http://*************:30024"`
- `model`: 模型名称，必须与VLLM服务中加载的模型一致

### parse函数参数

- `file_path`: 要解析的文档路径（支持PDF和图片）
- `skip_cross_page_merge`: 是否跳过跨页合并（默认False）
- `max_page_retries`: 单页最大重试次数（默认1）

## 返回结果格式

```python
{
    "orig_path": "document.pdf",           # 原始文件路径
    "num_pages": 5,                        # 总页数
    "document_text": "文档的markdown内容",  # 完整文档内容
    "page_texts": {                        # 各页内容
        "0": "第1页内容",
        "1": "第2页内容",
        ...
    },
    "fallback_pages": []                   # 解析失败的页面索引
}
```

## 错误处理

### 常见错误及解决方案

1. **连接错误**
   ```
   ConnectionError: No response from server
   ```
   - 检查VLLM服务是否正常运行
   - 验证URL和端口是否正确

2. **HTTP状态错误**
   ```
   ValueError: Error http status 500
   ```
   - 检查模型名称是否正确
   - 查看VLLM服务日志

3. **JSON解析错误**
   ```
   json.JSONDecodeError
   ```
   - 通常是模型输出格式问题，会自动重试

## 性能优化建议

1. **网络优化**
   - 使用高速网络连接
   - 考虑在同一局域网内部署

2. **并发处理**
   - 使用异步版本处理多个文档
   - 合理设置重试次数

3. **资源管理**
   - 监控VLLM服务的GPU内存使用
   - 根据需要调整`max_page_retries`

## 示例代码

完整的示例代码请参考 `remote_inference_example.py` 文件。

## 与原版本的兼容性

修改后的代码保持了与原版本的API兼容性，主要变化：

1. `parse`函数签名改变：
   - 原版本: `parse(llm, file_path, ...)`
   - 新版本: `parse(url, model, file_path, ...)`

2. 新增了`RemoteVLLMClient`类提供更友好的接口

3. 移除了对本地VLLM模型的依赖

## 故障排除

如果遇到问题，请检查：

1. VLLM服务是否正常运行
2. 网络连接是否正常
3. 模型名称是否匹配
4. 文件路径是否正确
5. 查看详细的错误日志

更多帮助请参考OCRFlux的官方文档。
