[build-system]
requires = ["setuptools", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "ocrflux"
description = "Fast, efficient, and high quality OCR powered by open visual language models"
version = "0.1.0"
readme = "README.md"
classifiers = [
    "Intended Audience :: Science/Research",
    "Development Status :: 3 - Alpha",
    "License :: OSI Approved :: Apache Software License",
    "Programming Language :: Python :: 3",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
]
authors = [
    {name = "Yu Tang", email = "<EMAIL>"}
]
requires-python = ">=3.11"
dependencies = [
  "cached-path",
  "smart_open",
  "pypdf>=5.2.0",
  "pypdfium2",
  "cryptography",
  "lingua-language-detector",
  "Pillow",
  "ftfy",
  "bleach",
  "markdown2",
  "filelock",
  "orjson",
  "requests",
  "zstandard",
  "boto3",
  "httpx",
  "torch>=2.5.1",
  "transformers==4.50.0",
  "vllm==0.7.3",
  "img2pdf",
  "nltk",
  "bs4",
  "distance",
  "apted",
  "gradio",
  "gradio_pdf",
]
license = {file = "LICENSE"}

[project.urls]
Homepage = "https://github.com/chatdoc-com/OCRFlux"
Repository = "https://github.com/chatdoc-com/OCRFlux"

[tool.setuptools.packages.find]
exclude = [
    "*.tests",
    "*.tests.*",
    "tests.*",
    "tests",
    "docs*",
    "scripts*",
    "images*"
]

[tool.setuptools]
include-package-data = true

[tool.setuptools.package-data]
ocrflux = [
    "py.typed",
]

[tool.black]
line-length = 79