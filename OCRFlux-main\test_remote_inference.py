#!/usr/bin/env python3
"""
测试远程VLLM推理功能

这个脚本用于测试修改后的inference.py是否能正常工作。
注意：这个测试需要远程VLLM服务正在运行。
"""

import sys
import os
from PIL import Image
import io

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def create_test_image():
    """创建一个测试图片"""
    # 创建一个简单的测试图片
    img = Image.new('RGB', (800, 600), color='white')
    
    # 保存为临时文件
    test_image_path = 'test_image.png'
    img.save(test_image_path)
    return test_image_path

def test_imports():
    """测试导入是否正常"""
    print("测试导入...")
    try:
        from ocrflux.inference import RemoteVLLMClient, parse, parse_async
        from ocrflux.inference import build_page_to_markdown_query, build_element_merge_detect_query, build_html_table_merge_query
        print("✓ 所有导入成功")
        return True
    except ImportError as e:
        print(f"✗ 导入失败: {e}")
        return False

def test_query_builders():
    """测试查询构建函数"""
    print("\n测试查询构建函数...")
    try:
        from ocrflux.inference import build_page_to_markdown_query, build_element_merge_detect_query, build_html_table_merge_query
        
        # 创建测试图片
        test_image_path = create_test_image()
        
        # 测试页面到markdown查询
        query1 = build_page_to_markdown_query("test-model", test_image_path, 1)
        assert "model" in query1
        assert "messages" in query1
        assert query1["model"] == "test-model"
        print("✓ build_page_to_markdown_query 正常")
        
        # 测试元素合并检测查询
        query2 = build_element_merge_detect_query("test-model", ["text1"], ["text2"])
        assert "model" in query2
        assert "messages" in query2
        print("✓ build_element_merge_detect_query 正常")
        
        # 测试HTML表格合并查询
        query3 = build_html_table_merge_query("test-model", "<table>test1</table>", "<table>test2</table>")
        assert "model" in query3
        assert "messages" in query3
        print("✓ build_html_table_merge_query 正常")
        
        # 清理测试文件
        if os.path.exists(test_image_path):
            os.remove(test_image_path)
            
        return True
    except Exception as e:
        print(f"✗ 查询构建函数测试失败: {e}")
        return False

def test_client_creation():
    """测试客户端创建"""
    print("\n测试客户端创建...")
    try:
        from ocrflux.inference import RemoteVLLMClient
        
        client = RemoteVLLMClient("http://localhost:30024", "test-model")
        assert client.url == "http://localhost:30024"
        assert client.model == "test-model"
        print("✓ RemoteVLLMClient 创建成功")
        return True
    except Exception as e:
        print(f"✗ 客户端创建失败: {e}")
        return False

def test_http_post_function():
    """测试HTTP POST函数（不实际发送请求）"""
    print("\n测试HTTP POST函数...")
    try:
        from ocrflux.inference import apost
        import asyncio
        
        # 这里只是测试函数是否可以导入和调用，不实际发送请求
        print("✓ apost 函数可以导入")
        return True
    except Exception as e:
        print(f"✗ HTTP POST函数测试失败: {e}")
        return False

def test_with_mock_server():
    """使用模拟服务器测试（如果可用）"""
    print("\n检查是否有可用的VLLM服务...")
    try:
        import requests
        
        # 尝试连接到本地VLLM服务
        test_urls = [
            "http://localhost:30024",
            "http://localhost:8000",
            "http://127.0.0.1:30024",
            "http://127.0.0.1:8000"
        ]
        
        for url in test_urls:
            try:
                response = requests.get(f"{url}/v1/models", timeout=2)
                if response.status_code == 200:
                    print(f"✓ 发现VLLM服务在: {url}")
                    return url
            except:
                continue
        
        print("✗ 未发现可用的VLLM服务")
        print("  提示：如果要进行完整测试，请先启动VLLM服务")
        return None
        
    except ImportError:
        print("✗ requests库未安装，跳过服务检查")
        return None

def main():
    """主测试函数"""
    print("=== OCRFlux 远程推理功能测试 ===\n")
    
    tests = [
        ("导入测试", test_imports),
        ("查询构建函数测试", test_query_builders),
        ("客户端创建测试", test_client_creation),
        ("HTTP函数测试", test_http_post_function),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"✗ {test_name} 出现异常: {e}")
    
    # 检查VLLM服务
    vllm_url = test_with_mock_server()
    
    print(f"\n=== 测试结果 ===")
    print(f"通过: {passed}/{total}")
    
    if passed == total:
        print("✓ 所有基础测试通过！")
        if vllm_url:
            print(f"✓ 发现VLLM服务，可以进行完整测试")
            print(f"  使用以下命令测试完整功能：")
            print(f"  python remote_inference_example.py")
        else:
            print("! 未发现VLLM服务，仅完成基础测试")
            print("  要进行完整测试，请先启动VLLM服务")
    else:
        print("✗ 部分测试失败，请检查代码")
        return 1
    
    return 0

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
